import sys
import json
from typing import List, Optional, Literal, Dict, Annotated, Union, TypeVar, Generic, Any, Type, Callable
from pydantic import BaseModel, Field
from enum import Enum


# ---- Definition of Antibody Drug Conjugate (ADC) Components ----

class LinkerType(str, Enum):
    """
    Enumeration of linker types used in Antibody Drug Conjugates (ADCs).

    Linkers are chemical compounds that connect the antibody to the cytotoxic payload.
    The type of linker affects drug release mechanism and therapeutic efficacy.
    """
    # Linkers that can be cleaved inside target cells or sometimes in tumour microenvironment to release the payload
    CLEAVABLE = "Cleavable Linker"

    # Linkers that remain attached to the payload after internalization
    NON_CLEAVABLE = "Non-cleavable Linker"

    # Used when linker information is not available or not applicable
    NONE = "NONE"


class AntibodyClonality(str, Enum):
    """
    Enumeration of antibody clonality types.

    Clonality refers to whether the antibody is derived from a single clone
    or multiple clones of B cells, affecting specificity and production.
    """
    # Antibodies derived from a single clone of B cells, providing high specificity
    MONOCLONAL = "Monoclonal Antibody (mAb)"

    # Antibodies derived from multiple clones of B cells, providing broader reactivity
    POLYCLONAL = "Polyclonal Antibody (pAb)"

    # Used when clonality information is not available or not applicable
    NONE = "NONE"


class AntibodySpecies(str, Enum):
    """
    Enumeration of antibody species origin and engineering types.

    This classification indicates the source organism and level of humanization,
    which affects immunogenicity and therapeutic potential in humans.
    """
    # Antibodies derived from mice, may cause immunogenic reactions in humans
    MURINE = "Murine"

    # Antibodies with mouse variable regions and human constant regions
    CHIMERIC = "Chimeric"

    # Antibodies with human framework regions and mouse complementarity-determining regions
    HUMANIZED = "Humanized"

    # Used when species information is not available or not applicable
    NONE = "NONE"


class AntibodyIsotype(str, Enum):
    """
    Enumeration of antibody isotype classifications.

    Isotypes are classes of antibodies defined by their heavy chain constant regions,
    each with distinct biological functions and properties.
    """
    # Immunoglobulin G - most common therapeutic antibody class
    IGG = "IgG"

    # Immunoglobulin M - pentameric antibody, first responder in immune response
    IGM = "IgM"

    # Immunoglobulin A - primarily found in mucosal areas and secretions
    IGA = "IgA"

    # Immunoglobulin E - involved in allergic reactions and parasitic infections
    IGE = "IgE"

    # Immunoglobulin D - functions as antigen receptor on B cells
    IGD = "IgD"

    # Used when isotype information is not available or not applicable
    NONE = "NONE"


class AntibodyDrugConjugateType(str, Enum):
    """
    Enumeration of ADC types based on their role in research studies.

    This classification distinguishes between ADCs being tested versus
    those used as reference standards or comparators.
    """
    # ADCs that are the primary subject of investigation in the study
    INVESTIGATIVE = "Investigative"

    # ADCs used as benchmarks, controls, or approved comparators
    REFERENCE = "Reference"

class AntibodyDrugConjugate(BaseModel):
    """
    Comprehensive model for Antibody Drug Conjugate (ADC) information extraction.

    This model captures detailed information about ADCs including their components
    (antibody, linker, payload), targets, and supporting citations from research literature.

    An ADC consists of three main components:
    1. Antibody: Provides specificity for target antigen on tumour cells
    2. Linker: Chemical bridge connecting antibody to payload
    3. Payload: Cytotoxic drug that provides therapeutic effect by causing tumour cell death
    """

    # Citation and source information
    citations: List[str] = Field(
        ...,
        description="Direct quotes or sentences from the research paper containing the extracted information"
    )

    # ADC identification and classification
    adc_name: str = Field(
        ...,
        description="Official or common name of the Antibody Drug Conjugate. If the ADC does not have a specific name, then create one by combining the antibody name, linker name and payload name.",
    )

    adc_type: AntibodyDrugConjugateType = Field(
        ...,
        description="Classification of ADC role in the study: 'Investigative' for test subjects, 'Reference' for approved comparators or controls",
    )

    # Antibody component fields
    antibody_name: str = Field(
        ...,
        description="Name or identifier of the monoclonal antibody component",
    )

    antibody_clonality: AntibodyClonality = Field(
        ...,
        description="Clonality classification indicating whether derived from single (monoclonal) or multiple (polyclonal) B cell clones",
    )

    antibody_species: AntibodySpecies = Field(
        ...,
        description="Species origin and humanization status affecting immunogenicity profile",
    )

    antibody_isotype: AntibodyIsotype = Field(
        ...,
        description="Heavy chain class determining antibody structure and biological functions",
    )

    # Payload component fields
    payload_name: str = Field(
        ...,
        description="Chemical name or identifier of the cytotoxic drug payload",
    )

    payload_target: str = Field(
        ...,
        description="Specific molecular target, pathway, or mechanism of action for the cytotoxic payload",
    )

    # Linker component fields
    linker_name: str = Field(
        ...,
        description="Chemical name or identifier of the linker molecule connecting antibody to payload",
    )

    linker_type: LinkerType = Field(
        ...,
        description="Cleavage mechanism classification determining payload release strategy inside target cells",
    )

    # Target antigen fields
    antigen_name: str = Field(
        ...,
        description="Name or identifier of the cell surface antigen specifically recognized by the antibody component",
    )

print("="*100)
print(json.dumps(AntibodyDrugConjugate.model_json_schema(), indent=2))
print("="*100)

# ---- Definition of Preclinical Experimental Models ----


class ExperimentalModel(BaseModel):
    """Information about experimental model used in the preclinical experiment for given ADC with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    model_name: str = Field(..., description="The name of the model used in the experiment for given ADC")
    model_type: ModelType = Field(..., description="The type of model used in the experiment for given ADC")
    cancer_type: str = Field(..., description="A cancer type refers to the broad, primary classification of cancer, based on the organ or tissue where the cancer originates. It is essentially the location in the body where the cancerous cells began to form.")
    cancer_subtype: Optional[str] = Field(None, description="A cancer subtype is a further classification within a cancer type, based on specific histological, genetic, or molecular characteristics of the tumor cells. Subtypes can influence the cancer's behavior, its response to treatment, and its prognosis.")
