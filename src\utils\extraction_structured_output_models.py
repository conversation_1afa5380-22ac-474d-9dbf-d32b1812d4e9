import sys
import json
from typing import List, Optional, Literal, Dict, Annotated, Union, TypeVar, Generic, Any, Type, Callable
from pydantic import BaseModel, Field
from enum import Enum


# ---- Definition of Antibody Drug Conjugate (ADC) Components ----

class LinkerType(str, Enum):
    """
    Enumeration of linker types used in Antibody Drug Conjugates (ADCs).

    Linkers are chemical compounds that connect the antibody to the cytotoxic payload.
    The type of linker affects drug release mechanism and therapeutic efficacy.
    """
    # Linkers that can be cleaved inside target cells or sometimes in tumour microenvironment to release the payload
    CLEAVABLE = "Cleavable Linker"

    # Linkers that remain attached to the payload after internalization
    NON_CLEAVABLE = "Non-cleavable Linker"

    # Used when linker information is not available or not applicable
    NONE = "NONE"


class AntibodyClonality(str, Enum):
    """
    Enumeration of antibody clonality types.

    Clonality refers to whether the antibody is derived from a single clone
    or multiple clones of B cells, affecting specificity and production.
    """
    # Antibodies derived from a single clone of B cells, providing high specificity
    MONOCLONAL = "Monoclonal Antibody (mAb)"

    # Antibodies derived from multiple clones of B cells, providing broader reactivity
    POLYCLONAL = "Polyclonal Antibody (pAb)"

    # Used when clonality information is not available or not applicable
    NONE = "NONE"


class AntibodySpecies(str, Enum):
    """
    Enumeration of antibody species origin and engineering types.

    This classification indicates the source organism and level of humanization,
    which affects immunogenicity and therapeutic potential in humans.
    """
    # Antibodies derived from mice, may cause immunogenic reactions in humans
    MURINE = "Murine"

    # Antibodies with mouse variable regions and human constant regions
    CHIMERIC = "Chimeric"

    # Antibodies with human framework regions and mouse complementarity-determining regions
    HUMANIZED = "Humanized"

    # Used when species information is not available or not applicable
    NONE = "NONE"


class AntibodyIsotype(str, Enum):
    """
    Enumeration of antibody isotype classifications.

    Isotypes are classes of antibodies defined by their heavy chain constant regions,
    each with distinct biological functions and properties.
    """
    # Immunoglobulin G - most common therapeutic antibody class
    IGG = "IgG"

    # Immunoglobulin M - pentameric antibody, first responder in immune response
    IGM = "IgM"

    # Immunoglobulin A - primarily found in mucosal areas and secretions
    IGA = "IgA"

    # Immunoglobulin E - involved in allergic reactions and parasitic infections
    IGE = "IgE"

    # Immunoglobulin D - functions as antigen receptor on B cells
    IGD = "IgD"

    # Used when isotype information is not available or not applicable
    NONE = "NONE"


class AntibodyDrugConjugateType(str, Enum):
    """
    Enumeration of ADC types based on their role in research studies.

    This classification distinguishes between ADCs being tested versus
    those used as reference standards or comparators.
    """
    # ADCs that are the primary subject of investigation in the study
    INVESTIGATIVE = "Investigative"

    # ADCs used as benchmarks, controls, or approved comparators
    REFERENCE = "Reference"

class AntibodyDrugConjugate(BaseModel):
    """
    Comprehensive model for Antibody Drug Conjugate (ADC) information extraction.

    This model captures detailed information about ADCs including their components
    (antibody, linker, payload), targets, and supporting citations from research literature.

    An ADC consists of three main components:
    1. Antibody: Provides specificity for target antigen on tumour cells
    2. Linker: Chemical bridge connecting antibody to payload
    3. Payload: Cytotoxic drug that provides therapeutic effect by causing tumour cell death
    """

    # Citation and source information
    citations: List[str] = Field(
        ...,
        description="Direct quotes or sentences from the research paper containing the extracted information"
    )

    # ADC identification and classification
    adc_name: str = Field(
        ...,
        description="Official or common name of the Antibody Drug Conjugate. If the ADC does not have a specific name, then create one by combining the antibody name, linker name and payload name.",
    )

    adc_type: AntibodyDrugConjugateType = Field(
        ...,
        description="Classification of ADC role in the study: 'Investigative' for test subjects, 'Reference' for approved comparators or controls",
    )

    # Antibody component fields
    antibody_name: str = Field(
        ...,
        description="Name or identifier of the monoclonal antibody component",
    )

    antibody_clonality: AntibodyClonality = Field(
        ...,
        description="Clonality classification indicating whether derived from single (monoclonal) or multiple (polyclonal) B cell clones",
    )

    antibody_species: AntibodySpecies = Field(
        ...,
        description="Species origin and humanization status affecting immunogenicity profile",
    )

    antibody_isotype: AntibodyIsotype = Field(
        ...,
        description="Heavy chain class determining antibody structure and biological functions",
    )

    # Payload component fields
    payload_name: str = Field(
        ...,
        description="Chemical name or identifier of the cytotoxic drug payload",
    )

    payload_target: str = Field(
        ...,
        description="Specific molecular target, pathway, or mechanism of action for the cytotoxic payload",
    )

    # Linker component fields
    linker_name: str = Field(
        ...,
        description="Chemical name or identifier of the linker molecule connecting antibody to payload",
    )

    linker_type: LinkerType = Field(
        ...,
        description="Cleavage mechanism classification determining payload release strategy inside target cells",
    )

    # Target antigen fields
    antigen_name: str = Field(
        ...,
        description="Name or identifier of the cell surface antigen specifically recognized by the antibody component",
    )

print("="*100)
print(json.dumps(AntibodyDrugConjugate.model_json_schema(), indent=2))
print("="*100)

# ---- Definition of Preclinical Experimental Models ----


class ModelType(str, Enum):
    """
    Hierarchical enumeration of experimental model types used in preclinical ADC research.

    This classification organizes experimental models into logical categories based on their
    biological complexity, clinical relevance, and experimental applications. Models progress
    from simple in vitro systems to complex in vivo models that better recapitulate human
    disease and therapeutic responses.

    Hierarchy:
    1. In Vitro Models: Cell-based laboratory systems
    2. Ex Vivo Models: Tissue-based systems outside the body
    3. In Vivo Models: Whole organism studies
       - Xenograft Models: Human cancer cells in immunocompromised hosts
       - Syngeneic Models: Mouse cancer cells in immunocompetent hosts
       - Transgenic Models: Genetically engineered cancer models
       - Non-Human Primate Models: Closest to human physiology
    """

    # === IN VITRO MODELS ===
    # Simple cell culture systems for initial screening and mechanistic studies
    CELL_LINE = "Cell Line Model"  # Immortalized cancer cell lines grown in culture; advantages: reproducible, cost-effective, high-throughput screening; limitations: lack tumor microenvironment, genetic drift over passages

    # === EX VIVO MODELS ===
    # Tissue-based models that preserve some native architecture
    ORGANOID = "Organoid Model"  # 3D tissue cultures derived from patient or cell line material; advantages: preserve tissue architecture, patient-specific responses; limitations: limited vascularization, simplified microenvironment

    TISSUE_SPECIMENS = "Tissue Specimens"  # Fresh or preserved human tissue samples for ex vivo drug testing; advantages: native tumor architecture, patient-specific; limitations: limited viability time, variability between specimens

    # === IN VIVO XENOGRAFT MODELS ===
    # Human cancer cells grown in immunocompromised animal hosts
    CDX = "Cell Line-Derived Xenograft (CDX)"  # Established cancer cell lines implanted in immunodeficient mice; advantages: reproducible, well-characterized, cost-effective; limitations: immunocompromised host, potential genetic drift

    PDX = "Patient-Derived Xenograft (PDX)"  # Primary patient tumor tissue directly implanted in immunodeficient mice; advantages: preserves patient tumor heterogeneity, clinically relevant; limitations: expensive, time-consuming, immunocompromised host

    # === IN VIVO SYNGENEIC MODELS ===
    # Mouse cancer cells in immunocompetent mouse hosts
    SYNGENEIC = "Syngeneic Model"  # Mouse cancer cell lines in immunocompetent mice of same genetic background; advantages: intact immune system, immune-oncology studies; limitations: mouse-specific biology, limited human relevance

    # === IN VIVO TRANSGENIC MODELS ===
    # Genetically engineered mouse models with spontaneous cancer development
    TRANSGENIC = "Transgenic Model"  # Genetically modified mice that spontaneously develop cancer; advantages: natural tumor development, intact immune system, genetic control; limitations: species differences, complex breeding requirements

    # === SPECIALIZED IN VIVO MODELS ===
    # Broader categories for specific research applications
    RODENT_MODELS = "Rodent Models"  # General category for mouse and rat models not otherwise specified; advantages: well-established protocols, regulatory acceptance; limitations: species differences in metabolism and physiology

    NON_HUMAN_PRIMATES = "Non-Human Primate Models"  # Primate models for safety and efficacy studies; advantages: closest to human physiology, regulatory requirement for some studies; limitations: ethical considerations, high cost, limited availability

    # === NON-CELLULAR MODELS ===
    # Alternative experimental systems
    NON_CELL_BASED = "Non-cell based Model"  # Biochemical assays, protein-based studies, or computational models; advantages: mechanistic insights, high-throughput screening; limitations: lack biological complexity

    # === UNDEFINED ===
    # Used when model type information is not available or not applicable
    NONE = "NONE"

class PreclinicalExperimentalModel(BaseModel):
    """
    Comprehensive model for preclinical experimental systems used in ADC research and testing.

    This model captures detailed information about the experimental platforms used to evaluate
    Antibody Drug Conjugates (ADCs) in preclinical studies. It encompasses the full spectrum
    of model systems from simple cell culture to complex animal models, providing context
    for interpreting experimental results and translating findings to clinical applications.

    The model includes information about:
    - Model system characteristics and classification
    - Cancer type and subtype being studied
    - Supporting citations from research literature

    This information is critical for understanding the relevance and limitations of
    preclinical ADC efficacy and safety data.
    """

    # Citation and source information
    citations: List[str] = Field(
        ...,
        description="Direct quotes or sentences from the research paper describing the experimental model system used",
        example=["HER2-positive breast cancer cell line BT-474 was used for in vitro studies.", "Patient-derived xenograft models were established from fresh tumor specimens."],
        min_items=1,
        max_items=8
    )

    # Model identification and classification
    model_name: str = Field(
        ...,
        description="Specific name or identifier of the experimental model system used in the ADC study",
        example="BT-474 cell line",
        min_length=2,
        max_length=150
    )

    model_type: ModelType = Field(
        ...,
        description="Classification of the experimental model system based on biological complexity and experimental approach",
        example="Cell Line Model"
    )

    # Cancer classification fields
    cancer_type: str = Field(
        ...,
        description="Primary cancer classification based on the organ or tissue of origin where the malignancy first developed",
        example="Breast cancer",
        min_length=3,
        max_length=100
    )

    cancer_subtype: Optional[str] = Field(
        None,
        description="Detailed cancer classification based on histological, molecular, or genetic characteristics that influence treatment response and prognosis",
        example="HER2-positive invasive ductal carcinoma",
        min_length=3,
        max_length=150
    )
